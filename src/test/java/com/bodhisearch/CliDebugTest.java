package com.bodhisearch;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import dasniko.testcontainers.keycloak.KeycloakContainer;

@Testcontainers
public class CliDebugTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(CliDebugTest.class);

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:23.0.7")
      .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
      .withProviderClassesFrom("target/classes")
  // .withDebugFixedPort(8787, true)
  ;

  @BeforeAll
  public static void importConfigs() {
    importFile(keycloak.getAuthServerUrl(), keycloak.getAdminUsername(),
        keycloak.getAdminPassword(),
        "./src/test/resources/import-files/bodhi-realm-debug-1.json");
  }

  public static void importFile(String keycloakUrl, String username, String password, String filename) {
    try {
      String[] command = { "java", "-jar",
          // "tools/keycloak-config-cli-24.0.5.jar",
          "tools/keycloak-config-cli-23.0.7.jar",
          "--import.files.locations=" + filename,
          "--keycloak.url=" + keycloakUrl,
          "--keycloak.user=" + username,
          "--keycloak.password=" + password,
          "--logging.level.keycloak-config-cli=debug"
      };
      Process process = Runtime.getRuntime().exec(command);
      BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
      BufferedReader stderrReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
      String line;

      while ((line = stdoutReader.readLine()) != null) {
        LOGGER.info(line);
      }

      while ((line = stderrReader.readLine()) != null) {
        LOGGER.error(line);
      }
      process.waitFor();

      int exitCode = process.exitValue();
      if (exitCode == 0) {
        System.out.println("CLI executed successfully.");
      } else {
        String errMsg = "CLI execution failed with exit code: " + exitCode;
        System.out.println(errMsg);
        throw new RuntimeException(errMsg);
      }
    } catch (IOException | InterruptedException e) {
      e.printStackTrace();
      throw new RuntimeException(e);
    }
  }

  @Test
  public void test() {
    System.out.println("Hello, World!");
  }
}
